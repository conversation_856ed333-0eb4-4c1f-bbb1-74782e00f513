<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C2 Command & Control Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: #000000;
            padding: 1rem 2rem;
            border-bottom: 3px solid #dc2626;
            box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
        }

        .header h1 {
            color: #dc2626;
            font-size: 2rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
        }

        .card h2 {
            color: #dc2626;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .clients-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .client-item {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .client-item:hover {
            border-color: #dc2626;
            background: rgba(220, 38, 38, 0.1);
        }

        .client-item.selected {
            border-color: #dc2626;
            background: rgba(220, 38, 38, 0.2);
        }

        .client-id {
            font-weight: bold;
            color: #dc2626;
        }

        .client-address {
            color: #cccccc;
            font-size: 0.9rem;
        }

        .command-section {
            margin-bottom: 1rem;
        }

        .command-input {
            width: 100%;
            padding: 0.75rem;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            margin-bottom: 1rem;
        }

        .command-input:focus {
            outline: none;
            border-color: #dc2626;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-right: 0.5rem;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #374151;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .output-section {
            grid-column: 1 / -1;
        }

        .output-area {
            background: #000000;
            border: 1px solid #dc2626;
            border-radius: 5px;
            padding: 1rem;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.5);
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-online {
            background: #10b981;
        }

        .status-offline {
            background: #ef4444;
        }

        .refresh-btn {
            background: none;
            border: 1px solid #dc2626;
            color: #dc2626;
            padding: 0.5rem 1rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .refresh-btn:hover {
            background: rgba(220, 38, 38, 0.1);
        }

        .server-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .server-btn {
            background: none;
            border: 1px solid;
            padding: 0.5rem 1rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s;
        }

        .server-btn.start {
            border-color: #16a34a;
            color: #16a34a;
        }

        .server-btn.start:hover {
            background: rgba(22, 163, 74, 0.1);
        }

        .server-btn.stop {
            border-color: #dc2626;
            color: #dc2626;
        }

        .server-btn.stop:hover {
            background: rgba(220, 38, 38, 0.1);
        }

        .server-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-indicator.status-offline {
            background: #ef4444;
        }

        .status-indicator.status-starting {
            background: #f59e0b;
            animation: pulse 1.5s infinite;
        }

        .status-indicator.status-stopping {
            background: #f59e0b;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 C2 Command & Control Dashboard</h1>
    </div>

    <div class="container">
        <div class="status-bar">
            <div>
                <span class="status-indicator" id="serverStatusIndicator"></span>
                <span id="serverStatusText">Server Status: Loading...</span>
            </div>
            <div class="server-controls">
                <button class="server-btn start" id="startBtn" onclick="startServer()" disabled>▶️ Start Server</button>
                <button class="server-btn stop" id="stopBtn" onclick="stopServer()" disabled>⏹️ Stop Server</button>
                <button class="refresh-btn" onclick="refreshClients()">🔄 Refresh</button>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h2>📡 Connected Clients</h2>
                <div class="clients-list" id="clientsList">
                    <div style="text-align: center; color: #666; padding: 2rem;">
                        Loading clients...
                    </div>
                </div>
            </div>

            <div class="card">
                <h2>⚡ Command Center</h2>
                <div class="command-section">
                    <input type="text"
                           class="command-input"
                           id="commandInput"
                           placeholder="Enter command (e.g., whoami, dir, ls -la)"
                           onkeypress="handleKeyPress(event)">

                    <button class="btn" onclick="sendCommand()">Send to Selected</button>
                    <button class="btn btn-secondary" onclick="sendToAll()">Send to All</button>
                </div>

                <div>
                    <strong>Selected Target:</strong>
                    <span id="selectedTarget">None</span>
                </div>
            </div>

            <div class="card output-section">
                <h2>📋 Command Output</h2>
                <div class="output-area" id="outputArea">

                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedClientId = null;
        let clients = [];
        let serverStatus = { running: false };

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize output area
            const outputArea = document.getElementById('outputArea');
            outputArea.textContent = `Welcome to C2 Dashboard
======================
Start the C2 server using the controls above, then select a client and enter commands to execute remotely.

Available commands depend on the target system:
- Windows: dir, whoami, systeminfo, tasklist, etc.
- Linux/Mac: ls, whoami, uname -a, ps aux, etc.

Command output will appear here...`;

            refreshServerStatus();
            refreshClients();
            setInterval(refreshServerStatus, 3000); // Check server status every 3 seconds
            setInterval(refreshClients, 5000); // Auto-refresh clients every 5 seconds
        });

        function refreshServerStatus() {
            fetch('/api/server/status')
                .then(response => response.json())
                .then(data => {
                    serverStatus = data;
                    updateServerStatus();
                })
                .catch(error => {
                    console.error('Error fetching server status:', error);
                    updateServerStatus({ running: false, error: true });
                });
        }

        function updateServerStatus(status = serverStatus) {
            const indicator = document.getElementById('serverStatusIndicator');
            const statusText = document.getElementById('serverStatusText');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');

            if (status.error) {
                indicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Server Status: Connection Error';
                startBtn.disabled = true;
                stopBtn.disabled = true;
                return;
            }

            if (status.running) {
                indicator.className = 'status-indicator status-online';
                statusText.textContent = `Server Status: Online (${status.client_count || 0} clients)`;
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                indicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Server Status: Offline';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        function startServer() {
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const indicator = document.getElementById('serverStatusIndicator');
            const statusText = document.getElementById('serverStatusText');

            // Update UI to show starting state
            indicator.className = 'status-indicator status-starting';
            statusText.textContent = 'Server Status: Starting...';
            startBtn.disabled = true;
            stopBtn.disabled = true;

            fetch('/api/server/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh status after a short delay
                    setTimeout(refreshServerStatus, 1000);
                } else {
                    alert('Failed to start server: ' + data.message);
                    refreshServerStatus();
                }
            })
            .catch(error => {
                console.error('Error starting server:', error);
                alert('Error starting server: ' + error);
                refreshServerStatus();
            });
        }

        function stopServer() {
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const indicator = document.getElementById('serverStatusIndicator');
            const statusText = document.getElementById('serverStatusText');

            // Update UI to show stopping state
            indicator.className = 'status-indicator status-stopping';
            statusText.textContent = 'Server Status: Stopping...';
            startBtn.disabled = true;
            stopBtn.disabled = true;

            fetch('/api/server/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh status after a short delay
                    setTimeout(refreshServerStatus, 1000);
                } else {
                    alert('Failed to stop server: ' + data.message);
                    refreshServerStatus();
                }
            })
            .catch(error => {
                console.error('Error stopping server:', error);
                alert('Error stopping server: ' + error);
                refreshServerStatus();
            });
        }

        function refreshClients() {
            // Only fetch clients if server is running
            if (!serverStatus.running) {
                document.getElementById('clientsList').innerHTML =
                    '<div style="text-align: center; color: #666; padding: 2rem;">Server is offline</div>';
                return;
            }

            fetch('/api/clients')
                .then(response => response.json())
                .then(data => {
                    clients = data;
                    updateClientsList();
                })
                .catch(error => {
                    console.error('Error fetching clients:', error);
                    document.getElementById('clientsList').innerHTML =
                        '<div style="color: #ef4444; text-align: center; padding: 2rem;">Error loading clients</div>';
                });
        }

        function updateClientsList() {
            const clientsList = document.getElementById('clientsList');

            if (clients.length === 0) {
                clientsList.innerHTML =
                    '<div style="text-align: center; color: #666; padding: 2rem;">No clients connected</div>';
                return;
            }

            clientsList.innerHTML = clients.map(client => `
                <div class="client-item ${selectedClientId === client.id ? 'selected' : ''}"
                     onclick="selectClient(${client.id})">
                    <div class="client-id">Client ID: ${client.id}</div>
                    <div class="client-address">📍 ${client.address}</div>
                    <div class="client-address">🕒 Connected: ${client.connected_at}</div>
                </div>
            `).join('');
        }

        function selectClient(clientId) {
            selectedClientId = clientId;
            document.getElementById('selectedTarget').textContent = `Client ${clientId}`;
            updateClientsList();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendCommand();
            }
        }

        function sendCommand() {
            if (!serverStatus.running) {
                alert('Server is not running. Please start the server first.');
                return;
            }
            if (!selectedClientId) {
                alert('Please select a client first');
                return;
            }
            executeCommand(selectedClientId);
        }

        function sendToAll() {
            if (!serverStatus.running) {
                alert('Server is not running. Please start the server first.');
                return;
            }
            executeCommand('all');
        }

        function executeCommand(targetId) {
            const command = document.getElementById('commandInput').value.trim();
            if (!command) {
                alert('Please enter a command');
                return;
            }

            const outputArea = document.getElementById('outputArea');
            const timestamp = new Date().toLocaleTimeString();

            outputArea.textContent += `\n[${timestamp}] Executing on ${targetId === 'all' ? 'ALL CLIENTS' : 'Client ' + targetId}: ${command}\n`;
            outputArea.scrollTop = outputArea.scrollHeight;

            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    client_id: targetId,
                    command: command
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputArea.textContent += `[${timestamp}] Command sent successfully. Waiting for response...\n`;

                    // Poll for response
                    pollForResponse(data.command_id, timestamp);
                } else {
                    outputArea.textContent += `[${timestamp}] Error: ${data.error}\n`;
                }
                outputArea.scrollTop = outputArea.scrollHeight;
            })
            .catch(error => {
                outputArea.textContent += `[${timestamp}] Network error: ${error}\n`;
                outputArea.scrollTop = outputArea.scrollHeight;
            });

            // Clear input
            document.getElementById('commandInput').value = '';
        }

        function pollForResponse(commandId, timestamp) {
            const outputArea = document.getElementById('outputArea');
            let attempts = 0;
            const maxAttempts = 20; // 20 seconds timeout

            const poll = () => {
                fetch(`/api/get_response/${commandId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'pending') {
                            attempts++;
                            if (attempts < maxAttempts) {
                                setTimeout(poll, 1000); // Poll every second
                            } else {
                                outputArea.textContent += `[${timestamp}] Timeout: No response received\n`;
                                outputArea.scrollTop = outputArea.scrollHeight;
                            }
                        } else {
                            // Response received
                            outputArea.textContent += `[${timestamp}] Response received:\n`;
                            outputArea.textContent += `${data.output}\n`;
                            outputArea.textContent += `[${timestamp}] Command completed (exit code: ${data.return_code})\n`;
                            outputArea.textContent += `${'='.repeat(50)}\n`;
                            outputArea.scrollTop = outputArea.scrollHeight;
                        }
                    })
                    .catch(error => {
                        outputArea.textContent += `[${timestamp}] Error polling response: ${error}\n`;
                        outputArea.scrollTop = outputArea.scrollHeight;
                    });
            };

            poll();
        }
    </script>
</body>
</html>